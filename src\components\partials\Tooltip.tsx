import React, { useState } from 'react';
import { Info } from 'react-feather';

interface TooltipProps {
    text: string;
    size?: number;
    className?: string;
}

const Tooltip: React.FC<TooltipProps> = ({ text, size = 16, className = '' }) => {
    const [isVisible, setIsVisible] = useState(false);

    return (
        <div className={`tooltip-container ${className}`} style={{ position: 'relative', display: 'inline-block' }}>
            <Info
                size={size}
                className="tooltip-icon"
                style={{
                    color: '#00AFF0',
                    cursor: 'pointer',
                    transition: 'color 0.2s ease',
                }}
                onMouseEnter={() => setIsVisible(true)}
                onMouseLeave={() => setIsVisible(false)}
            />
            {isVisible && (
                <div
                    className="tooltip-content"
                    style={{
                        position: 'absolute',
                        bottom: '100%',
                        left: '50%',
                        transform: 'translateX(-50%)',
                        marginBottom: '8px',
                        padding: '8px 12px',
                        backgroundColor: '#00AFF0',
                        color: '#fff',
                        borderRadius: '6px',
                        fontSize: '12px',
                        whiteSpace: 'nowrap',
                        zIndex: 1000,
                        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
                        opacity: isVisible ? 1 : 0,
                        transition: 'opacity 0.2s ease',
                        wordWrap: 'break-word',
                    }}
                >
                    {text}
                    <div
                        style={{
                            position: 'absolute',
                            top: '100%',
                            left: '50%',
                            transform: 'translateX(-50%)',
                            width: 0,
                            height: 0,
                            borderLeft: '5px solid transparent',
                            borderRight: '5px solid transparent',
                            borderTop: '5px solid #00AFF0',
                        }}
                    />
                </div>
            )}
        </div>
    );
};

export default Tooltip;
