import { ErrorCategory, ErrorLog } from '../../../../types/Logs';
import { useLayoutEffect } from 'react';
import { toggleModalOpen } from '../../../../utils/common';
import classNames from 'classnames';
import { FORMAT_DATE, formatDateTime } from '../../../../utils/date';

interface IProps {
    show: boolean;
    changeShow: (s: boolean) => void;
    errorLog?: ErrorLog;
}

export default function ErrorLogDetailModal({ show, changeShow, errorLog }: Readonly<IProps>) {
    useLayoutEffect(() => toggleModalOpen(show), [show]);

    const handleClose = () => {
        changeShow(false);
    };

    const generateErrorByCategory = (errorCategory?: ErrorCategory, errorLog?: ErrorLog) => {
        if (!errorCategory || !errorLog) return;
        switch (errorCategory) {
            case ErrorCategory.WORKFLOW:
                return (
                    <>
                        <div className="tw-flex tw-items-center">
                            <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                Workflow Definition Id:
                            </span>
                            <span className="tw-text-gray-900 tw-text-left">{errorLog.workflow_definition_id}</span>
                        </div>
                        {errorLog.workflow_definition && errorLog.workflow_definition.version && (
                            <div className="tw-flex tw-items-center">
                                <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                    Version:
                                </span>
                                <span className="tw-text-gray-900 tw-text-left">
                                    {errorLog.workflow_definition.version}
                                </span>
                            </div>
                        )}
                    </>
                );
            case ErrorCategory.REQUEST_MANAGEMENT:
                return (
                    <>
                        <div className="tw-flex tw-items-center">
                            <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                Workflow Instance Id:
                            </span>
                            <span className="tw-text-gray-900 tw-text-left">{errorLog.workflow_instance_id}</span>
                        </div>
                        {errorLog.workflow_instance && errorLog.workflow_instance.name && (
                            <div className="tw-flex tw-items-center">
                                <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                    Request Title:
                                </span>
                                <span className="tw-text-gray-900 tw-text-left">{errorLog.workflow_instance.name}</span>
                            </div>
                        )}
                    </>
                );
            case ErrorCategory.TASK_MANAGEMENT:
                return (
                    errorLog.user_task && (
                        <>
                            <div className="tw-flex tw-items-center">
                                <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                    Task Id:
                                </span>
                                <span className="tw-text-gray-900 tw-text-left">{errorLog.user_task.task_id}</span>
                            </div>
                            <div className="tw-flex tw-items-center">
                                <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                    Task Name:
                                </span>
                                <span className="tw-text-gray-900 tw-text-left">{errorLog.user_task.task_name}</span>
                            </div>
                            {errorLog.user_task.workflow_instance && (
                                <>
                                    <div className="tw-flex tw-items-center">
                                        <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                            Workflow Instance Id:
                                        </span>
                                        <span className="tw-text-gray-900 tw-text-left">
                                            {errorLog.user_task.workflow_instance.id}
                                        </span>
                                    </div>
                                    <div className="tw-flex tw-items-center">
                                        <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                            Request title:
                                        </span>
                                        <span className="tw-text-gray-900 tw-text-left">
                                            {errorLog.user_task.workflow_instance.name}
                                        </span>
                                    </div>
                                </>
                            )}
                        </>
                    )
                );
            case ErrorCategory.USER_MANAGEMENT:
                return (
                    <>
                        <div className="tw-flex tw-items-center">
                            <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">User ID:</span>
                            <span className="tw-text-gray-900 tw-text-left">{errorLog.user_id}</span>
                        </div>
                        {errorLog.user && (
                            <div className="tw-flex tw-items-center">
                                <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                    User Name:
                                </span>
                                <span className="tw-text-gray-900 tw-text-left">{errorLog.user.full_name}</span>
                            </div>
                        )}
                    </>
                );
            case ErrorCategory.FORM_MANAGEMENT:
                return (
                    <>
                        <div className="tw-flex tw-items-center">
                            <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">Form Id:</span>
                            <span className="tw-text-gray-900 tw-text-left">{errorLog.form_id}</span>
                        </div>
                        {errorLog.form && (
                            <div className="tw-flex tw-items-center">
                                <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                    Form Name:
                                </span>
                                <span className="tw-text-gray-900 tw-text-left">{errorLog.form.name}</span>
                            </div>
                        )}
                    </>
                );
            case ErrorCategory.AREA_MANAGEMENT:
                return errorLog.operationAreas && errorLog.operationAreas.length > 0 ? (
                    <>
                        <div className="tw-flex tw-items-center">
                            <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">Area Id:</span>
                            <span className="tw-text-gray-900 tw-text-left">{errorLog.operationAreas[0].id}</span>
                        </div>
                        <div className="tw-flex tw-items-center">
                            <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">Area Name:</span>
                            <span className="tw-text-gray-900 tw-text-left">{errorLog.operationAreas[0].name}</span>
                        </div>
                    </>
                ) : (
                    ''
                );
            default:
                return '';
        }
    };

    return (
        <>
            <div className={classNames('modal fade text-start', { show })} style={{ display: show ? 'block' : 'none' }}>
                <div className="modal-dialog modal-dialog-centered modal-lg">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">Error Log Detail: {errorLog?.id}</h5>
                            <button type="button" className="btn-close" onClick={handleClose} />
                        </div>
                        <div className="modal-body" style={{ maxHeight: '70vh', overflowY: 'auto' }}>
                            {/* Incident detail */}
                            <div className="tw-border tw-border-gray-300 tw-rounded tw-mb-4">
                                <div className="tw-bg-gray-100 tw-px-4 tw-py-3 tw-border-b tw-border-gray-300">
                                    <h6 className="tw-text-base tw-font-bold tw-text-gray-800 tw-mb-0">
                                        Incident Details:
                                    </h6>
                                </div>
                                <div className="tw-p-4">
                                    <div className="tw-space-y-3">
                                        {generateErrorByCategory(errorLog?.error_category as ErrorCategory, errorLog)}
                                        <div className="tw-flex tw-items-center">
                                            <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                                Time On Failure:
                                            </span>
                                            <span className="tw-text-gray-900 tw-text-left">
                                                {formatDateTime(errorLog?.created_at, FORMAT_DATE.SHOW_DATE_TIME)}
                                            </span>
                                        </div>
                                        <div className="tw-flex tw-items-center">
                                            <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                                Initiated By:
                                            </span>
                                            <span className="tw-text-gray-900 tw-text-left">
                                                {errorLog?.creator?.full_name ?? 'System'}
                                            </span>
                                        </div>
                                        {errorLog?.creator && (
                                            <div className="tw-flex tw-items-center">
                                                <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                                    Email:
                                                </span>
                                                <span className="tw-text-gray-900 tw-text-left">
                                                    {errorLog?.creator?.email}
                                                </span>
                                            </div>
                                        )}
                                        <div className="tw-flex tw-items-center">
                                            <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                                Failed Action:
                                            </span>
                                            <span className="tw-text-gray-900 tw-text-left">
                                                {errorLog?.error_category}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="tw-border tw-border-gray-300 tw-rounded tw-mb-4">
                                <div className="tw-bg-gray-100 tw-px-4 tw-py-3 tw-border-b tw-border-gray-300">
                                    <h6 className="tw-text-base tw-font-bold tw-text-gray-800 tw-mb-0">
                                        Error Summary:
                                    </h6>
                                </div>
                                <div className="tw-p-4">
                                    <div className="tw-space-y-3">
                                        <div className="tw-flex tw-items-center">
                                            <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                                Error Message:
                                            </span>
                                            <span className="tw-text-gray-900 tw-text-left">
                                                {errorLog?.error_message}
                                            </span>
                                        </div>
                                        <div className="tw-flex tw-items-center">
                                            <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                                API Endpoint:
                                            </span>
                                            <span className="tw-text-gray-900 tw-text-left">
                                                {errorLog?.api_endpoint}
                                            </span>
                                        </div>
                                        <div className="tw-flex tw-items-center">
                                            <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                                Log ID:
                                            </span>
                                            <span className="tw-text-gray-900 tw-text-left">{errorLog?.id}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {errorLog?.submit_value && (
                                <div className="tw-border tw-border-gray-300 tw-rounded tw-mb-4">
                                    <div className="tw-bg-gray-100 tw-px-4 tw-py-3 tw-border-b tw-border-gray-300">
                                        <h6 className="tw-text-base tw-font-bold tw-text-gray-800 tw-mb-0">
                                            Submit Value:
                                        </h6>
                                    </div>
                                    <div className="tw-p-4">
                                        <p className="tw-break-words tw-whitespace-pre-wrap tw-overflow-wrap-anywhere">
                                            {errorLog.submit_value}
                                        </p>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
