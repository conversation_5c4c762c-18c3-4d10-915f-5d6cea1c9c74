import React, { useMemo, useEffect } from 'react';
import Chart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';
import { STEP_COLORS } from '../moc_process/MOCStepSummary';
import { ProcessMocByArea, ProcessStatusByStep } from '../../../../types/Dashboard';
import { getColumnWidth } from '../moc_overview/MOCAreaChart';

// Helper function để chuyển hex sang rgba
const hexToRgba = (hex: string, alpha: number) => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

interface MOCHealthOverdueProps {
    overdueByStep: ProcessStatusByStep[];
    overdueByArea: ProcessMocByArea[];
    onStepColorMapChange?: (stepColorMap: Record<number, string>) => void;
}

const MOCHealthOverdue = ({ overdueByStep, overdueByArea, onStepColorMapChange }: Readonly<MOCHealthOverdueProps>) => {
    // Tính tổng count từ overdueByStep cho card Overdue
    const totalOverdueCount = useMemo(
        () => overdueByStep.reduce((total, step) => total + step.count, 0),
        [overdueByStep]
    );

    // Tạo mapping từ step.order đến màu tương ứng
    const stepColorMap = useMemo(
        () =>
            overdueByStep.reduce((map, step) => {
                map[step.order] = STEP_COLORS[step.order % STEP_COLORS.length];
                return map;
            }, {} as Record<number, string>),
        [overdueByStep]
    );

    // Gọi callback để truyền stepColorMap lên parent component
    useEffect(() => {
        if (onStepColorMapChange) {
            onStepColorMapChange(stepColorMap);
        }
    }, [stepColorMap, onStepColorMapChange]);

    // Chuẩn bị data cho chart từ overdueByArea
    const chartCategories = useMemo(() => overdueByArea.map((area) => area.name), [overdueByArea]);

    // Lấy tất cả các step từ overdueByStep để tạo series
    const chartSeries = useMemo(
        () =>
            overdueByStep.map((step) => ({
                name: step.name,
                data: overdueByArea.map((area) => {
                    const stepData = area.stack.find((s) => s.id === step.id);
                    return stepData ? stepData.count : 0;
                }),
                color: STEP_COLORS[step.order % STEP_COLORS.length],
            })),
        [overdueByStep, overdueByArea]
    );

    const dynamicColumnWidth = getColumnWidth(chartCategories.length);

    // Chart options
    const chartOptions: ApexOptions = {
        chart: {
            type: 'bar',
            stacked: true,
            toolbar: {
                show: false,
            },
        },
        plotOptions: {
            bar: {
                horizontal: false,
                columnWidth: dynamicColumnWidth,
                dataLabels: {
                    total: {
                        enabled: true,
                        style: {
                            fontSize: '12px',
                            fontWeight: 'bold',
                            color: '#373d3f',
                        },
                    },
                },
            },
        },
        dataLabels: {
            enabled: true,
            style: {
                fontSize: '11px',
                fontWeight: 'bold',
                colors: ['#ffffff'],
            },
            formatter: function (val: number) {
                return val > 0 ? val.toString() : '';
            },
        },
        xaxis: {
            categories: chartCategories,
            labels: {
                style: {
                    fontSize: '10px',
                },
                rotate: -45,
                rotateAlways: true,
                hideOverlappingLabels: false,
                showDuplicates: false,
                trim: false,
                maxHeight: 500,
            },
            tickAmount: chartCategories.length,
        },
        yaxis: {
            title: {
                text: 'Number of Overdue MOCs',
                style: {
                    fontSize: '12px',
                    fontWeight: 'bold',
                },
            },
            labels: {
                style: {
                    fontSize: '12px',
                },
            },
        },
        colors: overdueByStep.map((step) => STEP_COLORS[step.order % STEP_COLORS.length]),
        legend: {
            position: 'top',
            horizontalAlign: 'right',
            fontSize: '12px',
        },
        grid: {
            show: true,
            borderColor: '#e9ecef',
        },
        title: {
            text: 'Overdue MOCs by Area and Step',
            align: 'center',
            style: {
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#373d3f',
            },
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return val.toString();
                },
            },
        },
        responsive: [
            {
                breakpoint: 1000,
                options: {
                    xaxis: {
                        labels: {
                            rotate: -90,
                            style: {
                                fontSize: '8px',
                            },
                            rotateAlways: true,
                            hideOverlappingLabels: false,
                            showDuplicates: false,
                            trim: false,
                            maxHeight: 120,
                        },
                    },
                },
            },
        ],
    };

    return (
        <div className="tw-mb-6">
            <h4 className="tw-text-gray-600 tw-font-bold tw-text-xl tw-mb-4 tw-text-center">Unhealthy MOC</h4>

            {/* Header với Overdue và các Steps */}
            <div className="tw-flex tw-flex-wrap tw-items-center tw-justify-center tw-gap-4 tw-mb-4">
                {/* Overdue Card - cố định */}
                <div
                    className="tw-border-2 tw-bg-gray-50 tw-text-center tw-pb-3 tw-min-h-20 tw-justify-center"
                    style={{ borderColor: '#dc3545' }}
                >
                    <div
                        className="tw-font-bold tw-text-sm tw-p-2 tw-text-white"
                        style={{ backgroundColor: hexToRgba('#dc3545', 0.5) }}
                    >
                        Overdue
                    </div>
                    <div className="tw-text-2xl tw-font-bold tw-mt-2" style={{ color: '#dc3545' }}>
                        {totalOverdueCount}
                    </div>
                </div>

                {/* Các Steps */}
                {overdueByStep.map((step) => {
                    const stepColor = STEP_COLORS[step.order % STEP_COLORS.length];
                    return (
                        <div
                            key={step.id}
                            className="tw-border-2 tw-bg-gray-50 tw-text-center tw-pb-3 tw-min-h-20 tw-justify-center"
                            style={{ borderColor: stepColor }}
                        >
                            <div
                                className="tw-font-bold tw-text-sm tw-px-4 tw-py-2 tw-text-white"
                                style={{ backgroundColor: hexToRgba(stepColor, 0.5) }}
                            >
                                {step.name}
                            </div>
                            <div className="tw-text-2xl tw-font-bold tw-mt-2" style={{ color: stepColor }}>
                                {step.count}
                            </div>
                        </div>
                    );
                })}
            </div>

            {/* Chart */}
            <div className="tw-mt-6">
                {overdueByArea.length > 0 ? (
                    <Chart options={chartOptions} series={chartSeries} type="bar" height={400} />
                ) : (
                    <div className="tw-w-full tw-flex tw-justify-center tw-items-center tw-h-96">
                        <div className="tw-text-gray-500 tw-text-lg">No overdue data available</div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default MOCHealthOverdue;
