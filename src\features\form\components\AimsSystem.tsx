import classNames from 'classnames';
import { useEffect } from 'react';
import { Trash2 } from 'react-feather';
import { useFieldArray, Controller, Control } from 'react-hook-form';

interface AimsSystemProps {
    name: string;
    control: Control<any, any>;
    disabled?: boolean;
    isPreview?: boolean;
}

const statusOptions = ['[Status]', 'Completed', 'On going', 'N/A'];

export default function AimsSystem({ name, control, disabled, isPreview }: AimsSystemProps) {
    const isEditFormRoute = location.pathname.startsWith('/form/edit');
    const isFormAddRoute = location.pathname.startsWith('/form/add');
    const { fields, append, remove } = useFieldArray({
        control,
        name,
    });
    useEffect(() => {
        if (fields.length === 0) {
            append(
                {
                    document_no: '',
                    project_name: '',
                    status: '[Status]',
                    folder_link: '',
                },
                { shouldFocus: false }
            );
        }
    }, [append, fields.length]);

    return (
        <div className="tw-overflow-auto">
            <table className="tw-w-full tw-border tw-border-collapse">
                <thead>
                    <tr className="tw-bg-gray-100">
                        <th className="tw-border tw-p-2">No</th>
                        <th className="tw-border tw-p-2">Document No</th>
                        <th className="tw-border tw-p-2">Project Name</th>
                        <th className="tw-border tw-p-2">Status</th>
                        <th className="tw-border tw-p-2">Main Folder Link</th>
                        {!disabled && <th className="tw-border tw-p-2">Action</th>}
                    </tr>
                </thead>
                <tbody>
                    {fields.map((field, index) => (
                        <tr key={field.id}>
                            <td className="tw-border tw-p-1  tw-text-center">{index + 1}</td>
                            <td className="tw-border tw-p-1">
                                <Controller
                                    control={control}
                                    name={`${name}.${index}.document_no`}
                                    render={({ field }) => (
                                        <textarea
                                            className={classNames(
                                                'tw-outline-none tw-w-full tw-min-h-[60px] tw-bg-transparent',
                                                {
                                                    'tw-pointer-events-none':
                                                        (isEditFormRoute || isFormAddRoute) && !isPreview,
                                                }
                                            )}
                                            {...field}
                                            disabled={disabled}
                                        />
                                    )}
                                />
                            </td>
                            <td className="tw-border tw-p-1">
                                <Controller
                                    control={control}
                                    name={`${name}.${index}.project_name`}
                                    render={({ field }) => (
                                        <textarea
                                            className={classNames(
                                                'tw-outline-none tw-w-full tw-min-h-[60px] tw-bg-transparent',
                                                {
                                                    'tw-pointer-events-none':
                                                        (isEditFormRoute || isFormAddRoute) && !isPreview,
                                                }
                                            )}
                                            {...field}
                                            disabled={disabled}
                                        />
                                    )}
                                />
                            </td>
                            <td className="tw-border tw-p-1">
                                <Controller
                                    control={control}
                                    defaultValue="[Status]"
                                    name={`${name}.${index}.status`}
                                    render={({ field }) => (
                                        <select
                                            className={classNames(
                                                'tw-outline-none tw-pr-[20px] tw-flex tw-mt-[-34px] tw-bg-transparent',
                                                {
                                                    'tw-pointer-events-none':
                                                        (isEditFormRoute || isFormAddRoute) && !isPreview,
                                                }
                                            )}
                                            {...field}
                                            disabled={disabled}
                                        >
                                            {statusOptions.map((status) => (
                                                <option key={status} value={status}>
                                                    {status}
                                                </option>
                                            ))}
                                        </select>
                                    )}
                                />
                            </td>
                            <td className="tw-border tw-p-1">
                                <Controller
                                    control={control}
                                    name={`${name}.${index}.folder_link`}
                                    render={({ field }) => (
                                        <textarea
                                            className={classNames(
                                                'tw-outline-none tw-w-full tw-min-h-[60px] tw-bg-transparent',
                                                {
                                                    'tw-pointer-events-none':
                                                        (isEditFormRoute || isFormAddRoute) && !isPreview,
                                                }
                                            )}
                                            {...field}
                                            disabled={disabled}
                                        />
                                    )}
                                />
                            </td>
                            {!disabled && (
                                <td className="tw-border tw-p-1 tw-text-center">
                                    <button
                                        type="button"
                                        title="Delete"
                                        className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                        onClick={() => remove(index)}
                                        disabled={fields.length === 1}
                                    >
                                        <Trash2 size={14} />
                                    </button>
                                </td>
                            )}
                        </tr>
                    ))}

                    {!disabled && (
                        <tr>
                            <td colSpan={6} className="tw-text-start tw-p-1">
                                <button
                                    type="button"
                                    className={classNames('btn btn-primary waves-effect waves-light tw-min-h-[38px]', {
                                        'tw-pointer-events-none':
                                            disabled || ((isEditFormRoute || isFormAddRoute) && !isPreview),
                                    })}
                                    onClick={() =>
                                        append(
                                            {
                                                document_no: '',
                                                project_name: '',
                                                status: '[Status]',
                                                folder_link: '',
                                            },
                                            { shouldFocus: false }
                                        )
                                    }
                                >
                                    (+) Add Row
                                </button>
                            </td>
                        </tr>
                    )}
                </tbody>
            </table>
        </div>
    );
}
