import { useEffect, useState } from 'react';
import { ChevronDown, ChevronRight, Plus, X, Check, Edit, Folder, Search } from 'react-feather';
import Area, { AllAreaQueryRes, AreaCreateRes, AreaType, AreaUpdateRes } from '../../../types/OperationalArea';
import { QueryObserverResult, RefetchOptions } from '@tanstack/react-query';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { AREA_CREATE, AREA_OUT_OF_SERVICE, AREA_UPDATE } from '../../../services/AreaService';
import { showToast } from '../../../utils/common';
import { COMMON_MESSAGE } from '../../../constants/common';
import ModalConfirm from '../../../components/partials/ModalConfirm';
import NotInterestedIcon from '../../../components/partials/NotInterestedIcon';

interface IProps {
    areas: Area[];
    refetch: (options?: RefetchOptions | undefined) => Promise<QueryObserverResult<AllAreaQueryRes, Error>>;
}

const OperationalAreaTree = ({ areas, refetch }: Readonly<IProps>) => {
    const [data, setData] = useState<Area[]>([]);
    const [expandedNodes, setExpandedNodes] = useState<Set<number | string>>(new Set([1, 2, 9, 5]));
    const [searchTerm, setSearchTerm] = useState('');
    const [addingAt, setAddingAt] = useState<{ id: number | string; level: AreaType } | null>(null);
    const [editingAt, setEditingAt] = useState<{ id: number | string; level: AreaType; node: Area } | null>(null);
    const [newNodeName, setNewNodeName] = useState('');
    const [newNodeCode, setNewNodeCode] = useState('');
    const [errors, setErrors] = useState<{ name?: string; code?: string }>({});
    const [isDeleting, setIsDeleting] = useState(false);
    const [deletingId, setDeletingId] = useState<string | null>(null);

    useEffect(() => setData(areas), [setData, areas]);

    const validateInputs = (level: AreaType): { name?: string; code?: string } => {
        const newErrors: { name?: string; code?: string } = {};

        if (!newNodeName.trim()) {
            newErrors.name = COMMON_MESSAGE.FIELD_REQUIRED_NAME;
        } else if (newNodeName.length > 255) {
            newErrors.name = 'Name must not exceed 255 characters';
        }

        if (level === AreaType.SUB_AREA) {
            if (!newNodeCode.trim()) {
                newErrors.code = COMMON_MESSAGE.FIELD_REQUIRED_NAME;
            } else if (!/^[A-Za-z0-9]{2,5}$/.test(newNodeCode)) {
                newErrors.code = 'Code must be 2-5 characters long and contain only letters and numbers';
            }
        }

        return newErrors;
    };

    const { mutateAsync } = useGraphQLMutation<AreaCreateRes, { body: Partial<Area> }>(AREA_CREATE, '', {
        onSuccess: () => {
            showToast(true, ['Create area successfully']);
            refetch();
        },
    });

    const { mutate: updateMutate } = useGraphQLMutation<AreaUpdateRes, { id: string; body: Partial<Area> }>(
        AREA_UPDATE,
        '',
        {
            onSuccess: () => {
                showToast(true, ['Update area successfully']);
                refetch();
            },
        }
    );

    const toggleNode = (id: number | string) => {
        const newExpanded = new Set(expandedNodes);
        if (newExpanded.has(id)) {
            newExpanded.delete(id);
        } else {
            newExpanded.add(id);
        }
        setExpandedNodes(newExpanded);
    };

    const handleAddNode = (parentId: number | string | null, level: AreaType) => {
        setEditingAt(null);
        setAddingAt({ id: parentId || 0, level });
        setNewNodeName('');
        setNewNodeCode('');
        setErrors({});
    };

    const handleEditNode = (node: Area) => {
        setAddingAt(null);
        setEditingAt({ id: node.id!, level: node.type, node });
        setNewNodeName(node.name);
        setNewNodeCode(node.code || '');
        setErrors({});
    };

    const confirmAddNode = async () => {
        const newErrors = validateInputs(addingAt?.level || AreaType.ZONE);

        if (Object.keys(newErrors).length > 0) {
            setErrors(newErrors);
            return;
        }

        const body: Partial<Area> = {
            type: addingAt?.level || AreaType.ZONE,
            name: newNodeName.trim(),
            parent_area_id: addingAt?.id === 0 ? null : addingAt?.id?.toString(),
        };

        if (addingAt?.level === AreaType.SUB_AREA) {
            body.code = newNodeCode.trim();
        }

        await mutateAsync({ body: body });
        if (addingAt && addingAt.id !== 0) {
            setExpandedNodes((prev) => new Set([...prev, addingAt.id]));
        }

        setAddingAt(null);
        setErrors({});
    };

    const confirmEditNode = () => {
        const newErrors = validateInputs(editingAt?.level || AreaType.ZONE);

        if (Object.keys(newErrors).length > 0) {
            setErrors(newErrors);
            return;
        }

        const body: any = {
            type: editingAt?.level || AreaType.ZONE,
            name: newNodeName.trim(),
        };

        if (editingAt?.level === AreaType.SUB_AREA) {
            body.code = newNodeCode.trim();
        }

        updateMutate({
            id: editingAt?.id?.toString() || '',
            body: body,
        });

        setEditingAt(null);
        setErrors({});
    };

    const cancelAddNode = () => {
        setAddingAt(null);
        setErrors({});
    };

    const cancelEditNode = () => {
        setEditingAt(null);
        setErrors({});
    };

    const filterData = (nodes: Area[], term: string): Area[] => {
        if (!term) return nodes;

        return nodes
            .map((node) => {
                const nodeMatches = node.name.toLowerCase().includes(term.toLowerCase());

                const filteredChildren = node.children ? filterData(node.children, term) : undefined;

                if (nodeMatches || (filteredChildren && filteredChildren.length > 0)) {
                    return {
                        ...node,
                        children: filteredChildren,
                    };
                }

                return null;
            })
            .filter((node): node is NonNullable<typeof node> => node !== null);
    };

    const filteredData = filterData(data, searchTerm);

    const getPlaceholder = (level: AreaType) => {
        switch (level) {
            case AreaType.ZONE:
                return 'Enter new zone';
            case AreaType.MAIN_AREA:
                return 'Enter new main area';
            case AreaType.SUB_AREA:
                return 'Enter new sub area';
            case AreaType.FUNCTIONAL_AREA:
                return 'Enter new function area';
            default:
                return 'Enter new item';
        }
    };

    const deleteMutation = useGraphQLMutation<{ area_out_of_service: Area }, { id: string }>(AREA_OUT_OF_SERVICE, '', {
        onSuccess: () => {
            showToast(true, ['Set area out of service successfully']);
            setIsDeleting(false);
            setDeletingId(null);
            refetch();
        },
    });

    const handleDeleteNode = () => {
        if (deletingId) {
            deleteMutation.mutate({ id: deletingId });
        }
    };

    const renderTree = (nodes: Area[], level: AreaType = AreaType.ZONE) =>
        nodes.map((node) => {
            const hasChildren = node.children && node.children.length > 0;
            const isExpanded = expandedNodes.has(node.id!);

            return (
                <div key={node.id}>
                    <div className="d-flex align-items-center mb-1 position-relative">
                        <div
                            className="cursor-pointer me-50"
                            onClick={() => toggleNode(node.id!)}
                            style={{ width: '20px' }}
                        >
                            {hasChildren && (isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />)}
                        </div>

                        <div
                            className="d-flex align-items-center flex-grow-1 pe-3"
                            style={{ cursor: 'pointer', minWidth: 0 }}
                        >
                            <div className="me-50">
                                <div className="folder-icon">
                                    {level < AreaType.FUNCTIONAL_AREA && <Folder size={20} className="text-primary" />}
                                </div>
                            </div>

                            {editingAt?.id === node.id ? (
                                <div className="d-flex align-items-center">
                                    <input
                                        type="text"
                                        className={`form-control form-control-sm ${errors.name ? 'is-invalid' : ''}`}
                                        placeholder={getPlaceholder(editingAt?.level || AreaType.ZONE)}
                                        value={newNodeName}
                                        onChange={handleNameChange}
                                        maxLength={255}
                                        autoFocus
                                        style={{ width: editingAt?.level === AreaType.SUB_AREA ? '200px' : '300px' }}
                                    />
                                    {editingAt?.level === AreaType.SUB_AREA && (
                                        <input
                                            type="text"
                                            className={`form-control form-control-sm ms-1 ${
                                                errors.code ? 'is-invalid' : ''
                                            }`}
                                            placeholder="Enter area code"
                                            value={newNodeCode}
                                            onChange={handleCodeChange}
                                            maxLength={5}
                                            style={{ width: '150px' }}
                                        />
                                    )}
                                </div>
                            ) : (
                                <span
                                    onClick={() => toggleNode(node.id!)}
                                    title={`${node.name}${
                                        node.type === AreaType.SUB_AREA && node.code ? ` (${node.code})` : ''
                                    }`}
                                    style={{
                                        display: '-webkit-box',
                                        WebkitLineClamp: 2,
                                        WebkitBoxOrient: 'vertical',
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                        lineHeight: '1.2em',
                                        maxHeight: '2.4em',
                                        wordBreak: 'break-word',
                                    }}
                                >
                                    {node.name}
                                    {node.type === AreaType.SUB_AREA && node.code ? ` (${node.code})` : ''}
                                </span>
                            )}
                        </div>

                        <div className="d-flex align-items-center flex-shrink-0 ms-auto">
                            {node.out_of_service ? (
                                <span className="text-danger fw-bold small">Out of service</span>
                            ) : editingAt?.id === node.id ? (
                                <div className="d-flex">
                                    <button type="button" className="btn btn-sm btn-icon me-1" onClick={cancelEditNode}>
                                        <X size={16} className="text-danger" />
                                    </button>
                                    <button type="button" className="btn btn-sm btn-icon" onClick={confirmEditNode}>
                                        <Check size={16} className="text-success" />
                                    </button>
                                </div>
                            ) : (
                                <div className="d-flex">
                                    <button
                                        type="button"
                                        className="btn btn-sm btn-icon"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handleEditNode(node);
                                        }}
                                        title={'Edit'}
                                    >
                                        <Edit size={16} className="text-primary" />
                                    </button>
                                    {level < AreaType.FUNCTIONAL_AREA && (
                                        <button
                                            type="button"
                                            className="btn btn-sm btn-icon"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                const nextLevel =
                                                    level === AreaType.ZONE
                                                        ? AreaType.MAIN_AREA
                                                        : level === AreaType.MAIN_AREA
                                                        ? AreaType.SUB_AREA
                                                        : AreaType.FUNCTIONAL_AREA;
                                                handleAddNode(node.id!, nextLevel);
                                            }}
                                            title={'Add'}
                                        >
                                            <Plus size={16} className="text-primary" />
                                        </button>
                                    )}
                                    <button
                                        type="button"
                                        className="btn btn-sm btn-icon"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            setIsDeleting(true);
                                            setDeletingId(node.id!.toString());
                                        }}
                                        title={'Set out of service'}
                                    >
                                        <NotInterestedIcon size={16} className="text-danger" />
                                    </button>
                                </div>
                            )}
                        </div>
                    </div>

                    {editingAt?.id === node.id && (errors.name || errors.code) && (
                        <div className="ms-5">
                            {errors.name && <div className="text-danger small mb-1">{errors.name}</div>}
                            {errors.code && <div className="text-danger small mb-1">{errors.code}</div>}
                        </div>
                    )}

                    {hasChildren && isExpanded && (
                        <div className="ms-5">
                            {node.children &&
                                renderTree(
                                    node.children,
                                    level === AreaType.ZONE
                                        ? AreaType.MAIN_AREA
                                        : level === AreaType.MAIN_AREA
                                        ? AreaType.SUB_AREA
                                        : AreaType.FUNCTIONAL_AREA
                                )}
                        </div>
                    )}

                    {addingAt?.id === node.id && (
                        <div className="ms-5">
                            <div className="d-flex align-items-center mb-1 position-relative">
                                <div className="cursor-pointer me-50" style={{ width: '20px' }}></div>

                                <div
                                    className="d-flex align-items-center flex-grow-1 pe-3"
                                    style={{ cursor: 'pointer', minWidth: 0 }}
                                >
                                    <div className="me-50">
                                        <div className="folder-icon">
                                            {addingAt?.level && addingAt?.level < AreaType.FUNCTIONAL_AREA && (
                                                <Folder size={20} className="text-primary" />
                                            )}
                                        </div>
                                    </div>

                                    <div className="d-flex align-items-center">
                                        <input
                                            type="text"
                                            className={`form-control form-control-sm ${
                                                errors.name ? 'is-invalid' : ''
                                            }`}
                                            placeholder={getPlaceholder(addingAt?.level || AreaType.ZONE)}
                                            value={newNodeName}
                                            onChange={handleNameChange}
                                            maxLength={255}
                                            autoFocus
                                            style={{ width: addingAt?.level === AreaType.SUB_AREA ? '200px' : '300px' }}
                                        />
                                        {addingAt?.level === AreaType.SUB_AREA && (
                                            <input
                                                type="text"
                                                className={`form-control form-control-sm ms-1 ${
                                                    errors.code ? 'is-invalid' : ''
                                                }`}
                                                placeholder="Enter area code"
                                                value={newNodeCode}
                                                onChange={handleCodeChange}
                                                maxLength={5}
                                                style={{ width: '150px' }}
                                            />
                                        )}
                                    </div>
                                </div>

                                <div className="d-flex align-items-center flex-shrink-0 ms-auto">
                                    <button type="button" className="btn btn-sm btn-icon me-1" onClick={cancelAddNode}>
                                        <X size={16} className="text-danger" />
                                    </button>
                                    <button type="button" className="btn btn-sm btn-icon" onClick={confirmAddNode}>
                                        <Check size={16} className="text-success" />
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}
                    {addingAt?.id === node.id && errors.name && (
                        <div className="text-danger small mb-1 ms-5">{errors.name}</div>
                    )}
                    {addingAt?.id === node.id && errors.code && (
                        <div className="text-danger small mb-1 ms-5">{errors.code}</div>
                    )}
                </div>
            );
        });

    const handleCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value.toUpperCase();
        if (value === '' || /^[A-Za-z0-9]{0,5}$/.test(value)) {
            setNewNodeCode(value);
        }
    };

    const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        if (value.length <= 255) {
            setNewNodeName(value);
        }
    };

    return (
        <>
            <div className="card">
                <div className="card-body">
                    <div className="d-flex justify-content-between align-items-center mb-1">
                        <h4 className="card-title mb-0">Operational Area</h4>
                        <div className="d-flex align-items-stretch">
                            <div className="input-group input-group-merge me-1">
                                <span className="input-group-text" style={{ padding: '0.25rem 0.5rem' }}>
                                    <Search size={14} />
                                </span>
                                <input
                                    type="text"
                                    className="form-control form-control-sm"
                                    placeholder="Search area"
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    style={{ height: '36px' }}
                                />
                            </div>
                            <button
                                className="btn btn-primary btn-sm d-flex align-items-center"
                                onClick={() => handleAddNode(null, AreaType.ZONE)}
                                style={{
                                    minWidth: '130px',
                                    justifyContent: 'center',
                                    height: '36px',
                                }}
                            >
                                <Plus size={14} className="me-25" /> New zone
                            </button>
                        </div>
                    </div>

                    <div
                        className="border rounded"
                        style={{ height: 'calc(100vh - 250px)', overflowY: 'auto', overflowX: 'auto' }}
                    >
                        <div className="p-1 tw-w-[800px]">
                            {renderTree(filteredData)}

                            {addingAt?.level === AreaType.ZONE && addingAt.id === 0 && (
                                <div className="mb-1">
                                    <div className="d-flex align-items-center mb-1 position-relative">
                                        <div className="cursor-pointer me-50" style={{ width: '20px' }}></div>

                                        <div
                                            className="d-flex align-items-center flex-grow-1 pe-3"
                                            style={{ cursor: 'pointer', minWidth: 0 }}
                                        >
                                            <div className="me-50">
                                                <div className="folder-icon">
                                                    <Folder size={20} className="text-primary" />
                                                </div>
                                            </div>

                                            <input
                                                type="text"
                                                className={`form-control form-control-sm ${
                                                    errors.name ? 'is-invalid' : ''
                                                }`}
                                                placeholder="Enter new zone"
                                                value={newNodeName}
                                                onChange={handleNameChange}
                                                maxLength={255}
                                                autoFocus
                                                style={{ width: '300px' }}
                                            />
                                        </div>

                                        <div className="d-flex align-items-center flex-shrink-0 ms-auto">
                                            <button className="btn btn-sm btn-icon me-1" onClick={cancelAddNode}>
                                                <X size={16} className="text-danger" />
                                            </button>
                                            <button className="btn btn-sm btn-icon" onClick={confirmAddNode}>
                                                <Check size={16} className="text-success" />
                                            </button>
                                        </div>
                                    </div>
                                    {errors.name && <div className="text-danger small mb-1">{errors.name}</div>}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
            <ModalConfirm
                show={isDeleting}
                text={`Are you sure you want to mark this area as "Out of Service"? This will prevent it and all its child areas from being selected in new MOC requests.`}
                btnDisabled={false}
                changeShow={setIsDeleting}
                submitAction={handleDeleteNode}
                isDelete={true}
                textTitle={'Confirm Out of Service'}
            />
        </>
    );
};

export default OperationalAreaTree;
