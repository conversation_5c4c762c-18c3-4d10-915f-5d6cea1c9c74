import classNames from 'classnames';
import { useLayoutEffect } from 'react';
import { toggleModalOpen } from '../../../../utils/common';
import { AuditLog, AuditLogActionType, AuditLogActionTypeNames } from '../../../../types/Logs';
import { FORMAT_DATE, formatDateTime } from '../../../../utils/date';

interface IProps {
    show: boolean;
    changeShow: (s: boolean) => void;
    auditLog?: AuditLog;
}

export default function AuditLogDetailModal({ show, changeShow, auditLog }: Readonly<IProps>) {
    useLayoutEffect(() => toggleModalOpen(show), [show]);

    const handleClose = () => {
        changeShow(false);
    };

    let logRoles = '';
    if (
        [
            AuditLogActionType.MOC_CREATE_DRAFT,
            AuditLogActionType.MOC_SUBMIT_NEW,
            AuditLogActionType.MOC_UPDATE_DRAFT,
            AuditLogActionType.MOC_DELETE_DRAFT,
        ].includes(auditLog?.action_type as AuditLogActionType)
    ) {
        logRoles = 'Originator';
    }

    if (
        [AuditLogActionType.MOC_APPROVE, AuditLogActionType.MOC_REJECT, AuditLogActionType.MOC_TERMINATE].includes(
            auditLog?.action_type as AuditLogActionType
        )
    ) {
        logRoles = auditLog?.userTask.userRoles.map((role) => role.name).join(', ') ?? '';
    }

    return (
        <>
            <div className={classNames('modal fade text-start', { show })} style={{ display: show ? 'block' : 'none' }}>
                <div className="modal-dialog modal-dialog-centered modal-lg">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">Audit Log Detail: {auditLog?.id}</h5>
                            <button type="button" className="btn-close" onClick={handleClose} />
                        </div>
                        <div className="modal-body" style={{ maxHeight: '70vh', overflowY: 'auto' }}>
                            {/* Event Details Section */}
                            <div className="tw-border tw-border-gray-300 tw-rounded tw-mb-4">
                                <div className="tw-bg-gray-100 tw-px-4 tw-py-3 tw-border-b tw-border-gray-300">
                                    <h6 className="tw-text-base tw-font-bold tw-text-gray-800 tw-mb-0">
                                        Event Details:
                                    </h6>
                                </div>
                                <div className="tw-p-4">
                                    <div className="tw-space-y-3">
                                        <div className="tw-flex tw-items-center">
                                            <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                                Timestamp:
                                            </span>
                                            <span className="tw-text-gray-900 tw-text-left">
                                                {formatDateTime(auditLog?.created_at, FORMAT_DATE.SHOW_DATE_MINUTE)}
                                            </span>
                                        </div>
                                        <div className="tw-flex tw-items-center">
                                            <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                                Action Type:
                                            </span>
                                            <span className="tw-text-gray-900 tw-text-left">
                                                {
                                                    AuditLogActionTypeNames.find(
                                                        (item) => item.id === auditLog?.action_type
                                                    )?.name
                                                }
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* User & Session Information Section */}
                            <div className="tw-border tw-border-gray-300 tw-rounded tw-mb-4">
                                <div className="tw-bg-gray-100 tw-px-4 tw-py-3 tw-border-b tw-border-gray-300">
                                    <h6 className="tw-text-base tw-font-bold tw-text-gray-800 tw-mb-0">
                                        User & Session Information
                                    </h6>
                                </div>
                                <div className="tw-p-4">
                                    <div className="tw-space-y-3">
                                        <div className="tw-flex tw-items-center">
                                            <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                                User Name:
                                            </span>
                                            <span className="tw-text-gray-900 tw-text-left">
                                                {auditLog?.creator?.full_name}
                                            </span>
                                        </div>
                                        <div className="tw-flex tw-items-center">
                                            <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                                Empl. Code:
                                            </span>
                                            <span className="tw-text-gray-900 tw-text-left">
                                                {auditLog?.creator?.oms_emp_code}
                                            </span>
                                        </div>
                                        <div className="tw-flex tw-items-center">
                                            <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                                Email:
                                            </span>
                                            <span className="tw-text-gray-900 tw-text-left">
                                                {auditLog?.creator?.email}
                                            </span>
                                        </div>
                                        <div className="tw-flex tw-items-center">
                                            <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                                IP Address:
                                            </span>
                                            <span className="tw-text-gray-900 tw-text-left">
                                                {auditLog?.ip_address}
                                            </span>
                                        </div>
                                        <div className="tw-flex tw-items-center">
                                            <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                                User Agent:
                                            </span>
                                            <span
                                                className="tw-text-gray-900 tw-text-left"
                                                title={auditLog?.user_agent}
                                            >
                                                {auditLog?.user_agent}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Moc context */}
                            {auditLog?.mocRequest && (
                                <div className="tw-border tw-border-gray-300 tw-rounded tw-mb-4">
                                    <div className="tw-bg-gray-100 tw-px-4 tw-py-3 tw-border-b tw-border-gray-300">
                                        <h6 className="tw-text-base tw-font-bold tw-text-gray-800 tw-mb-0">
                                            MOC Context
                                        </h6>
                                    </div>
                                    <div className="tw-p-4">
                                        <div className="tw-space-y-3">
                                            <div className="tw-flex tw-items-center">
                                                <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                                    MOC Request Title:
                                                </span>
                                                <span className="tw-text-gray-900 tw-text-left">
                                                    {auditLog?.mocRequest?.name}
                                                </span>
                                            </div>
                                            <div className="tw-flex tw-items-center">
                                                <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                                    MOC No:
                                                </span>
                                                <span className="tw-text-gray-900 tw-text-left">
                                                    {auditLog?.mocRequest?.business_key}
                                                </span>
                                            </div>
                                            <div className="tw-flex tw-items-center">
                                                <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                                    Step Name:
                                                </span>
                                                <span className="tw-text-gray-900 tw-text-left">
                                                    {auditLog?.workflow_step?.step_name}
                                                </span>
                                            </div>
                                            <div className="tw-flex tw-items-center">
                                                <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                                    Current MOC Status:
                                                </span>
                                                <span className="tw-text-gray-900 tw-text-left">
                                                    {auditLog?.mocRequest?.status}
                                                </span>
                                            </div>
                                            <div className="tw-flex tw-items-center">
                                                <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                                    Role (at action):
                                                </span>
                                                <span className="tw-text-gray-900 tw-text-left">{logRoles}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Remarks / System details */}
                            <div className="tw-border tw-border-gray-300 tw-rounded tw-mb-4">
                                <div className="tw-bg-gray-100 tw-px-4 tw-py-3 tw-border-b tw-border-gray-300">
                                    <h6 className="tw-text-base tw-font-bold tw-text-gray-800 tw-mb-0">
                                        System Details
                                    </h6>
                                </div>
                                <div className="tw-p-4">
                                    <div className="tw-space-y-3">
                                        <div className="tw-flex tw-items-center">
                                            <span className="tw-font-medium tw-text-gray-600 tw-w-48 tw-flex-shrink-0">
                                                Details:
                                            </span>
                                            <span className="tw-text-gray-900 tw-text-left">
                                                {auditLog?.description}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="modal-footer">
                            <button type="button" className="btn btn-secondary" onClick={handleClose}>
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
