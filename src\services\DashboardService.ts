import { gql } from 'graphql-request';

export const DASHBOARD_MOC_STATUS_SUMMARY = gql`
    query Dashboard_moc_status_summary($search: String, $filters: [String!], $sort: String) {
        dashboard_moc_status_summary(body: { search: $search, filters: $filters, sort: $sort }) {
            on_tracking
            completed
            cancelled
            overdue
            backlog
            cancelled
        }
    }
`;

export const DASHBOARD_MOC_AREA_SUMMARY = gql`
    query Dashboard_moc_area_summary($search: String, $filters: [String!], $sort: String) {
        dashboard_moc_area_summary(body: { search: $search, filters: $filters, sort: $sort }) {
            id
            name
            type
            code
            summary {
                on_tracking
                completed
                cancelled
                overdue
                backlog
            }
        }
    }
`;

export const DASHBOARD_MOC_TYPE_SUMMARY = gql`
    query Dashboard_moc_type_summary($search: String, $filters: [String!], $sort: String) {
        dashboard_moc_type_summary(body: { search: $search, filters: $filters, sort: $sort }) {
            type
            summary {
                software
                facility_non_process
                facility_process
            }
        }
    }
`;

export const DASHBOARD_MOC_WORKFLOW_INSTANCE = gql`
    query Dashboard_moc_workflow_instances(
        $page: Int!
        $limit: Int!
        $filters: [String!]
        $sort: String
        $search: String
    ) {
        dashboard_moc_workflow_instances(
            body: { search: $search, filters: $filters, sort: $sort, page: $page, limit: $limit }
        ) {
            totalCount
            totalPages
            currentPage
            data {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                workflow_definition_id
                process_instance_id
                camunda_key
                business_key
                name
                status
                started_at
                ended_at
                form_data
                camunda_variables
                type
                sub_area_ids
                current_user_task_id
                tracking_status
                due_date
                overdue
                backlog
                sub_category
                current_user_task {
                    id
                    workflow_step {
                        id
                        step_name
                        step_order
                    }
                }
                creator {
                    id
                    full_name
                }
                area {
                    id
                    name
                }
                workflow_instance_areas {
                    id
                    area_id
                    workflow_instance_id
                    area {
                        id
                        name
                    }
                }
            }
        }
    }
`;

export const DASHBOARD_MOC_HEALTH_SUMMARY = gql`
    query Dashboard_moc_health_summary($search: String, $filters: [String!], $sort: String) {
        dashboard_moc_health_summary(body: { search: $search, filters: $filters, sort: $sort }) {
            overdue_by_step {
                id
                name
                order
                key
                count
            }
            overdue_by_area {
                id
                name
                type
                code
                stack {
                    id
                    name
                    order
                    key
                    count
                }
            }
            backlog_by_area {
                id
                name
                code
                type
                count
            }
        }
    }
`;

export const DASHBOARD_MOC_PROCESS_SUMMARY = gql`
    query Dashboard_moc_process_summary($search: String, $filters: [String!], $sort: String) {
        dashboard_moc_process_summary(body: { search: $search, filters: $filters, sort: $sort }) {
            status_by_step {
                id
                name
                order
                key
                count
            }
            area_stacker_by_step {
                id
                name
                type
                code
                stack {
                    id
                    name
                    order
                    key
                    count
                }
            }
            time_line_stacker_by_step {
                name
                month
                year
                stack {
                    id
                    name
                    order
                    key
                    count
                }
            }
        }
    }
`;

export const DASHBOARD_MOC_PROCESS_WORKFLOW_INSTANCE = gql`
    query Dashboard_moc_process_workflow_instances(
        $page: Int!
        $limit: Int!
        $filters: [String!]
        $sort: String
        $search: String
    ) {
        dashboard_moc_process_workflow_instances(
            body: { search: $search, filters: $filters, sort: $sort, page: $page, limit: $limit }
        ) {
            totalCount
            totalPages
            currentPage
            data {
                id
                created_by
                updated_by
                created_at
                updated_at
                deleted_at
                workflow_definition_id
                process_instance_id
                camunda_key
                business_key
                name
                status
                started_at
                ended_at
                form_data
                camunda_variables
                type
                sub_area_ids
                current_user_task_id
                tracking_status
                due_date
                overdue
                backlog
                sub_category
                process_status
                current_user_task {
                    id
                    workflow_step {
                        id
                        step_name
                        step_order
                    }
                }
                creator {
                    id
                    full_name
                }
                area {
                    id
                    name
                }
                workflow_instance_areas {
                    id
                    area_id
                    workflow_instance_id
                    area {
                        id
                        name
                    }
                }
            }
        }
    }
`;
