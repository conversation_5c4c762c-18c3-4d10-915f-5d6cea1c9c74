import { BaseModelString, BaseSearch, DataList, FilterConfig } from './common';
import { FormEntity, UserTaskEntity, WorkflowDefinition, WorkflowInstance, WorkflowStepsType } from './Workflow';
import { UserAccount } from './User';
import Area from './OperationalArea';
import { FILTER_CONDITIONS } from '../constants/common';
import Role from './Role';

export enum ErrorCategory {
    INTEGRATION = 'INTEGRATION',
    SYSTEM = 'SYSTEM',
    WORKFLOW = 'WORKFLOW',
    AUTHENTICATION = 'AUTHENTICATION',
    REQUEST_MANAGEMENT = 'REQUEST_MANAGEMENT',
    TASK_MANAGEMENT = 'TASK_MANAGEMENT',
    USER_MANAGEMENT = 'USER_MANAGEMENT',
    FORM_MANAGEMENT = 'FORM_MANAGEMENT',
    AREA_MANAGEMENT = 'AREA_MANAGEMENT',
    NOTIFICATION = 'NOTIFICATION',
    REPORTING = 'REPORTING',
}

export const errorCategoryNames = [
    { id: ErrorCategory.INTEGRATION, name: 'Integration' },
    { id: ErrorCategory.WORKFLOW, name: 'Workflow' },
    { id: ErrorCategory.REQUEST_MANAGEMENT, name: 'Request Management' },
    { id: ErrorCategory.TASK_MANAGEMENT, name: 'Task Management' },
    { id: ErrorCategory.USER_MANAGEMENT, name: 'User Management' },
    { id: ErrorCategory.FORM_MANAGEMENT, name: 'Form Management' },
    { id: ErrorCategory.REPORTING, name: 'Reporting / Analytics' },
    { id: ErrorCategory.NOTIFICATION, name: 'Notification' },
    { id: ErrorCategory.AUTHENTICATION, name: 'Authentication' },
    { id: ErrorCategory.SYSTEM, name: 'System' },
    { id: ErrorCategory.AREA_MANAGEMENT, name: 'Area Management' },
    // { id: ErrorCategory.UNKNOWN, name: 'Unknown' },
];

export enum System {
    OMS = 'OMS',
    MOC_SYSTEM = 'MOC_SYSTEM',
}

export const SystemNames = [
    { id: System.OMS, name: 'OMS' },
    { id: System.MOC_SYSTEM, name: 'MOC System' },
];

export interface AuditLog extends BaseModelString {
    role_id: string;
    action_type: string;
    moc_request_id: string;
    workflow_step_id: string;
    description: string;
    ip_address: string;
    user_agent: string;
    workflow_definition_id: string;
    updated_user_id: string;
    user_task_id: string;
    operation_area_ids: string[];
    workflow_step: WorkflowStepsType;
    workflowDefinition: WorkflowDefinition;
    updatedUser: UserAccount;
    userTask: UserTaskEntity;
    operationAreas: Area[];
    mocRequest: WorkflowInstance;
    role: Role;
}

export enum AuditLogActionType {
    // MOC Lifecycle
    MOC_CREATE_DRAFT = 'MOC_CREATE_DRAFT',
    MOC_SUBMIT_NEW = 'MOC_SUBMIT_NEW',
    MOC_UPDATE_DRAFT = 'MOC_UPDATE_DRAFT',
    MOC_APPROVE = 'MOC_APPROVE',
    MOC_REJECT = 'MOC_REJECT',
    MOC_TERMINATE = 'MOC_TERMINATE',
    MOC_DELETE_DRAFT = 'MOC_DELETE_DRAFT',

    // User & Role Management
    USER_ROLE_ASSIGN = 'USER_ROLE_ASSIGN',
    USER_ACTIVATE = 'USER_ACTIVATE',
    USER_DEACTIVATE = 'USER_DEACTIVATE',
    USER_UPDATE = 'USER_UPDATE',

    // System Configuration
    WORKFLOW_DEF_CREATE = 'WORKFLOW_DEF_CREATE',
    WORKFLOW_DEF_UPDATE = 'WORKFLOW_DEF_UPDATE',
    WORKFLOW_DEF_ACTIVATE = 'WORKFLOW_DEF_ACTIVATE',
    WORKFLOW_DEF_DEACTIVATE = 'WORKFLOW_DEF_DEACTIVATE',
    WORKFLOW_DEF_DELETE = 'WORKFLOW_DEF_DELETE',
    OPERATION_AREA_CREATE = 'OPERATION_AREA_CREATE',
    OPERATION_AREA_UPDATE = 'OPERATION_AREA_UPDATE',
    OPERATION_AREA_STATUS = 'OPERATION_AREA_STATUS',

    // Task Management
    TASK_ASSIGN = 'TASK_ASSIGN',
    TASK_DELEGATE = 'TASK_DELEGATE',

    //FORM
    FORM_DEF_CREATE = 'FORM_DEF_CREATE',
    FORM_DEF_UPDATE = 'FORM_DEF_UPDATE',
    FORM_DEF_ACTIVATE = 'FORM_DEF_ACTIVATE',
    FORM_DEF_INACTIVATE = 'FORM_DEF_INACTIVATE',
    FORM_DEF_DELETE = 'FORM_DEF_DELETE',
}

export const AuditLogActionTypeNames = [
    { id: AuditLogActionType.MOC_CREATE_DRAFT, name: 'MOC Create Draft' },
    { id: AuditLogActionType.MOC_SUBMIT_NEW, name: 'MOC Submit New' },
    { id: AuditLogActionType.MOC_UPDATE_DRAFT, name: 'MOC Update Draft' },
    { id: AuditLogActionType.MOC_APPROVE, name: 'MOC Approve' },
    { id: AuditLogActionType.MOC_REJECT, name: 'MOC Reject' },
    { id: AuditLogActionType.MOC_TERMINATE, name: 'MOC Terminate' },
    { id: AuditLogActionType.MOC_DELETE_DRAFT, name: 'MOC Delete Draft' },
    { id: AuditLogActionType.USER_ROLE_ASSIGN, name: 'MOC Assign Role' },
    { id: AuditLogActionType.USER_ACTIVATE, name: 'Activate User' },
    { id: AuditLogActionType.USER_DEACTIVATE, name: 'Deactivate User' },
    { id: AuditLogActionType.USER_UPDATE, name: 'Update User' },
    { id: AuditLogActionType.WORKFLOW_DEF_CREATE, name: 'Create Workflow Version' },
    { id: AuditLogActionType.WORKFLOW_DEF_UPDATE, name: 'Update Workflow Version' },
    { id: AuditLogActionType.WORKFLOW_DEF_ACTIVATE, name: 'Activate Workflow Version' },
    { id: AuditLogActionType.WORKFLOW_DEF_DEACTIVATE, name: 'Deactivate Workflow Version' },
    { id: AuditLogActionType.WORKFLOW_DEF_DELETE, name: 'Delete Workflow Version' },
    { id: AuditLogActionType.OPERATION_AREA_CREATE, name: 'Create Operation Area' },
    { id: AuditLogActionType.OPERATION_AREA_UPDATE, name: 'Update Operation Area' },
    { id: AuditLogActionType.OPERATION_AREA_STATUS, name: 'Out Of Service Operation Area' },
    { id: AuditLogActionType.TASK_ASSIGN, name: 'Assign Task' },
    { id: AuditLogActionType.TASK_DELEGATE, name: 'Delegate Task' },
    { id: AuditLogActionType.FORM_DEF_CREATE, name: 'Create Form' },
    { id: AuditLogActionType.FORM_DEF_UPDATE, name: 'Update Form' },
    { id: AuditLogActionType.FORM_DEF_ACTIVATE, name: 'Activate Form' },
    { id: AuditLogActionType.FORM_DEF_INACTIVATE, name: 'Inactivate Form' },
    { id: AuditLogActionType.FORM_DEF_DELETE, name: 'Delete Form' },
];

export interface AuditLogQuery {
    audit_log_list: DataList<AuditLog>;
}

export interface SearchAuditLog extends BaseSearch {
    created_at_from?: string;
    created_at_to?: string;
    created_at__range?: string;
    action_type?: string;
}

export type SearchAuditLogParam = {
    [key in keyof SearchAuditLog]: string;
};

export const auditLogFilterConfig: FilterConfig = {
    created_at_from: { key: 'created_at', operator: FILTER_CONDITIONS.GREATER_OR_EQUAL },
    created_at_to: { key: 'created_at', operator: FILTER_CONDITIONS.LESS_OR_EQUAL },
    action_type: { key: 'action_type', operator: FILTER_CONDITIONS.IN },
};

export interface ErrorLog extends BaseModelString {
    error_category: string;
    api_endpoint: string;
    error_message: string;
    user_id: string;
    ip_address: string;
    user_agent: string;
    user_task_id: string;
    form_id: string;
    workflow_instance_id: string;
    workflow_step_id: string;
    workflow_definition_id: string;
    operation_area_ids: string[];
    submit_value: string;
    role_ids: string[];
    form: FormEntity;
    user: UserAccount;
    user_task: UserTaskEntity;
    workflow_instance: WorkflowInstance;
    workflow_step: WorkflowStepsType;
    workflow_definition: WorkflowDefinition;
    operationAreas: Area[];
    roles: Role[];
    system: System;
}

export interface ErrorLogQuery {
    error_logs_list: DataList<ErrorLog>;
}

export interface SearchErrorLog extends BaseSearch {
    created_at_from?: string;
    created_at_to?: string;
    created_at__range?: string;
    error_category?: string;
    error_message?: string;
    system?: string;
}

export type SearchErrorLogParam = {
    [key in keyof SearchErrorLog]: string;
};

export const errorLogFilterConfig: FilterConfig = {
    created_at_from: { key: 'created_at', operator: FILTER_CONDITIONS.GREATER_OR_EQUAL },
    created_at_to: { key: 'created_at', operator: FILTER_CONDITIONS.LESS_OR_EQUAL },
    error_category: { key: 'error_category', operator: FILTER_CONDITIONS.IN },
    error_message: { key: 'error_message', operator: FILTER_CONDITIONS.LIKE },
    system: { key: 'system', operator: FILTER_CONDITIONS.IN },
};

export interface AuditLogDashboard {
    total: number;
}

export interface ErrorLogDashboard {
    total: number;
    oms: number;
    system: number;
}
