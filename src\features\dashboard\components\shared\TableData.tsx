import { getStatusBadgeWorkflow } from '../../../../utils/common';
import PaginationTable from '../../../../components/partials/PaginationTable';
import { DashboardMocWorkflowInstances } from '../../../../types/Dashboard';
import { workflowInstanceTypeNames } from '../../../../types/Workflow';
import { FORMAT_DATE, formatDateTime } from '../../../../utils/date';
import { DataList } from '../../../../types/common';

interface Iprops {
    mocWorkflowInstanceData: DataList<DashboardMocWorkflowInstances> | undefined;
    onChangePage: (page: number) => void;
    isProcess?: boolean;
    stepColorMap?: Record<number, string>;
}

export function TableData({
    mocWorkflowInstanceData,
    onChangePage,
    isProcess = false,
    stepColorMap,
}: Readonly<Iprops>) {
    const mocWorkflowInstance = mocWorkflowInstanceData?.data || [];

    // Function để render badge cho Current Step
    const renderCurrentStepBadge = (stepName: string | undefined, stepOrder: number | undefined) => {
        if (!stepName) return '-';

        if (stepColorMap && stepOrder !== undefined) {
            const color = stepColorMap[stepOrder];
            if (color) {
                return (
                    <span className="badge rounded-pill text-white" style={{ backgroundColor: color }}>
                        {stepName}
                    </span>
                );
            }
        }

        // Fallback nếu không tìm thấy màu tương ứng
        return <span className="badge rounded-pill bg-light-secondary">{stepName}</span>;
    };

    return (
        <>
            <div className="table-responsive">
                {isProcess && (
                    <div className="tw-w-full tw-flex tw-items-center tw-justify-center tw-mb-4">
                        <h4 className="tw-text-[#333] tw-font-bold tw-text-[14px]">Inprogress MOCs detail</h4>
                    </div>
                )}
                <table className="table table-sm align-middle mb-0">
                    <thead>
                        <tr>
                            <th>MOC No.</th>
                            <th>Title</th>
                            <th>Areas</th>
                            <th>Type</th>
                            <th>Current Step</th>
                            <th>Due Date</th>
                            <th className="text-center">{isProcess ? 'MOC Status' : 'Status'}</th>
                            <th>Originator</th>
                            {!isProcess && (
                                <>
                                    <th>Overdue</th>
                                    <th>Backlog</th>
                                </>
                            )}
                            {isProcess && (
                                <th>
                                    Schedule Status (Up to {formatDateTime(new Date(), FORMAT_DATE.SHOW_ONLY_DATE)})
                                </th>
                            )}
                        </tr>
                    </thead>
                    <tbody>
                        {mocWorkflowInstance.map((item) => {
                            const areasName = item.workflow_instance_areas?.map((wf) => wf.area.name).join(', ');
                            return (
                                <tr key={item.business_key}>
                                    <td>{item.business_key}</td>
                                    <td>{item.name}</td>
                                    <td>{areasName}</td>
                                    <td>
                                        {
                                            workflowInstanceTypeNames.find(
                                                (instanceType) => instanceType.id === item.type
                                            )?.name
                                        }
                                    </td>
                                    <td>
                                        {renderCurrentStepBadge(
                                            item.current_user_task?.workflow_step?.step_name,
                                            item.current_user_task?.workflow_step?.step_order
                                        )}
                                    </td>
                                    <td>
                                        {item.due_date ? formatDateTime(item.due_date, FORMAT_DATE.SHORT_DATE) : '-'}
                                    </td>
                                    <td className="text-center">{getStatusBadgeWorkflow(item.status)}</td>
                                    <td>{item.creator?.full_name}</td>
                                    {!isProcess && (
                                        <>
                                            <td>
                                                {!item.due_date
                                                    ? '-'
                                                    : item.overdue
                                                    ? `${item.overdue} Day${item.overdue > 1 ? 's' : ''}`
                                                    : '-'}
                                            </td>
                                            <td>
                                                {!item.due_date
                                                    ? '-'
                                                    : item.backlog
                                                    ? +`Yes (${item.backlog} Day${item.backlog > 1 ? 's' : ''})`
                                                    : 'No'}
                                            </td>
                                        </>
                                    )}
                                    {isProcess && <td>{}</td>}
                                </tr>
                            );
                        })}
                    </tbody>
                </table>
            </div>
            <PaginationTable
                countItem={mocWorkflowInstanceData?.totalCount || 0}
                totalPage={mocWorkflowInstanceData?.totalPages || 1}
                currentPage={mocWorkflowInstanceData?.currentPage || 1}
                handlePageChange={(event, page) => onChangePage(page)}
            />
        </>
    );
}
