/* eslint-disable react-hooks/exhaustive-deps */
import FormSettings from '../components/FormSettings';
import FormFields from '../components/FormFields';
import { Form, FormDetail, FormItem, FormStatus } from 'types/Form';
import { showToast } from 'utils/common';
import { useNavigate } from 'react-router-dom';
import { useGraphQLMutation } from 'hooks/useGraphQLMutation';
import { FORM_CREATE, FORM_DETAIL, FORM_UPDATE, FORM_UPDATE_BASE_INFO } from 'services/FormService';
import { useAppStore } from 'stores/appStore';
import { useEffect, useState } from 'react';
import ModalContent from 'components/partials/ModalContent';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { keepPreviousData } from '@tanstack/react-query';
import { QUERY_KEY } from 'constants/common';
import { getStatusBadge } from '../components/ListForm';
import Spinner from 'components/partials/Spinner';
import { nanoid } from 'nanoid';
import { FORMAT_DATE, formatDateTime } from 'utils/date';
import classNames from 'classnames';
import { PiPencilSimpleLine } from 'react-icons/pi';
import { useTranslation } from 'react-i18next';
import * as yup from 'yup';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';

interface IProps {
    isSuperAdmin?: boolean;
    isAdmin?: boolean;
    id?: string;
}

const FormBuilder = ({ id, isSuperAdmin = false, isAdmin = false }: Readonly<IProps>) => {
    const navigate = useNavigate();
    const { t } = useTranslation();
    const isLoadingApp = useAppStore((state) => state.isLoadingApp);
    const setIsLoadingApp = useAppStore((state) => state.setIsLoadingApp);
    const [formName, setFormName] = useState('');
    const [formDescription, setFormDescription] = useState('');
    const isFormAddRoute = location.pathname.startsWith('/form/add');

    const [isPreview, setIsPreview] = useState(false);
    const [isEditNameAndDes, setIsEditNameAndDes] = useState(false);

    const setActiveFields = useAppStore((state) => state.setActiveFields);
    const setActiveOption = useAppStore((state) => state.setActiveOption);

    const yupObj = {
        name: yup.string().required(t('error.required')).trim(),
        description: yup.string().required(t('error.required')).trim(),
    };

    const schema = yup.object(yupObj).required();
    const {
        register,
        handleSubmit,
        reset,
        formState: { errors },
    } = useForm<{ name: string; description: string }>({
        resolver: yupResolver(schema),
    });

    const updateBaseInfoMutation = useGraphQLMutation<Form, { id: string; name: string; description: string }>(
        FORM_UPDATE_BASE_INFO,
        '',
        {
            onSuccess: () => {
                showToast(true, ['Update form successfully']);
            },
            onSettled: () => setIsLoadingApp(false),
        }
    );

    const onSubmit = async (formData: { name: string; description: string }) => {
        if (id) {
            updateBaseInfoMutation.mutate({
                id,
                name: formData.name,
                description: formData.description,
            });
        }
        setFormName(formData.name);
        setFormDescription(formData.description);
        setIsEditNameAndDes(!isEditNameAndDes);
    };

    const onPreviewForm = () => {
        setIsPreview(!isPreview);
        setActiveFields(null);
        setActiveOption(null);
    };

    const onEditFormInformation = () => {
        reset({
            name: formName || '',
            description: formDescription || '',
        });
        setIsEditNameAndDes(!isEditNameAndDes);
    };

    const [schemaForm, setSchemaForm] = useState<FormItem>({
        form_key: `form_${nanoid()}`,
        form_fields: [],
        settings: {
            title: 'Form Title',
            formAlignment: 'left',
            previousForm: [],
        },
    });

    const { data, isLoading, isRefetching, isSuccess } = useGraphQLQuery<FormDetail>(
        [QUERY_KEY.FORM_DETAIL, id],
        FORM_DETAIL,
        { id },
        '',
        {
            enabled: !!id,
            placeholderData: keepPreviousData,
        }
    );

    useEffect(() => {
        setIsLoadingApp(false);
    }, [isSuccess]);

    const createFormMutation = useGraphQLMutation(FORM_CREATE, '', {
        onSuccess: () => {
            showToast(true, ['Create form successfully']);
            navigate('/form');
        },
        onSettled: () => setIsLoadingApp(false),
    });

    const updateFormMutation = useGraphQLMutation(FORM_UPDATE, '', {
        onSuccess: () => {
            showToast(true, ['Update form successfully']);
            navigate('/form');
        },
        onSettled: () => setIsLoadingApp(false),
    });

    useEffect(() => {
        data && setSchemaForm(data?.form_detail?.schema);
        data && setFormName(data?.form_detail?.name);
        data && setFormDescription(data?.form_detail?.description);
        if (data?.form_detail) {
            reset({
                name: data.form_detail.name || '',
                description: data.form_detail.description || '',
            });
        }
    }, [data, reset]);

    const onCreateForm = async () => {
        if (formName && formDescription) {
            setIsLoadingApp(true);
            createFormMutation.mutate({
                input: {
                    name: formName,
                    schema: schemaForm,
                    description: formDescription,
                },
            });
        } else if (!formName || !formDescription) {
            setIsEditNameAndDes(!isEditNameAndDes);
        }
    };
    const onCreateNewForm = async () => {
        if (formName && formDescription) {
            setIsLoadingApp(true);
            updateFormMutation.mutate({
                input: {
                    id,
                    name: formName,
                    schema: { ...schemaForm, form_key: `form_${nanoid()}` },
                    description: formDescription,
                },
            });
        } else if (!formName || !formDescription) {
            setIsEditNameAndDes(!isEditNameAndDes);
        }
    };

    const onUpdateForm = async () => {
        if (formName && formDescription) {
            setIsLoadingApp(true);
            updateFormMutation.mutate({
                input: {
                    id,
                    name: formName,
                    description: formDescription,
                    schema: schemaForm,
                },
            });
        } else if (!formName || !formDescription) {
            setIsEditNameAndDes(!isEditNameAndDes);
        }
    };

    const usedIn =
        data?.form_detail?.workflow_forms && data?.form_detail?.workflow_forms?.length > 0
            ? data?.form_detail?.workflow_forms
                  ?.map((item) => `${item?.task_name} - ${item?.workflow_definition?.name}`)
                  .join(', ')
            : '';

    return (
        <>
            <div className="content-body">
                {(isLoading || isRefetching || isLoadingApp) && <Spinner />}
                {!isLoading && !isRefetching && !isLoadingApp && (
                    <>
                        <div className="tw-border-b tw-border-b-[#e6e6e8] tw-p-4 tw-bg-white">
                            <div className="tw-flex tw-gap-4 tw-flex-col">
                                <div className="tw-flex tw-gap-4 tw-justify-between tw-w-full tw-items-center">
                                    <div className="tw-flex tw-gap-3 tw-items-start tw-w-full">
                                        <div className="tw-flex tw-gap-6 tw-items-end tw-w-full">
                                            <div>
                                                <label htmlFor="formName" className="tw-font-medium">
                                                    Form Name
                                                </label>
                                                <input
                                                    type="text"
                                                    placeholder="Form Name *"
                                                    value={formName}
                                                    className="form-control tw-pb-2 tw-w-[300px] !tw-bg-white"
                                                    disabled
                                                    title={formName}
                                                    id="formName"
                                                />
                                            </div>
                                            <div>
                                                <label htmlFor="formDescription" className="tw-font-medium">
                                                    Form Description
                                                </label>
                                                <input
                                                    placeholder="Form Description *"
                                                    value={formDescription}
                                                    className="form-control tw-pb-2 tw-w-[300px] !tw-bg-white"
                                                    disabled
                                                    title={formDescription}
                                                    id="formDescription"
                                                />
                                            </div>
                                            <PiPencilSimpleLine
                                                size={20}
                                                title="Edit Form Information"
                                                className="tw-cursor-pointer tw-flex-shrink-0"
                                                onClick={onEditFormInformation}
                                            />
                                        </div>
                                    </div>
                                    {(isSuperAdmin || isAdmin) && (
                                        <div className="tw-flex tw-gap-4 tw-flex-shrink-0 tw-justify-end">
                                            {id && data?.form_detail.status === FormStatus.DRAFT && (
                                                <button
                                                    type="button"
                                                    className="btn btn-primary waves-effect waves-light"
                                                    onClick={onUpdateForm}
                                                >
                                                    Save Draft
                                                </button>
                                            )}

                                            {!id && (
                                                <button
                                                    type="button"
                                                    className="btn btn-primary waves-effect waves-light"
                                                    onClick={onCreateForm}
                                                >
                                                    Save Draft
                                                </button>
                                            )}
                                            <button
                                                type="button"
                                                className="btn btn-primary waves-effect waves-light"
                                                onClick={onPreviewForm}
                                            >
                                                Preview
                                            </button>
                                            {((id && data?.form_detail.status === FormStatus.ACTIVE) ||
                                                data?.form_detail.status === FormStatus.INACTIVE) && (
                                                <button
                                                    type="button"
                                                    className="btn btn-primary waves-effect waves-light"
                                                    onClick={onCreateNewForm}
                                                >
                                                    Save As New Version
                                                </button>
                                            )}
                                        </div>
                                    )}
                                </div>
                                {id && data?.form_detail && (
                                    <div className="tw-flex tw-gap-4 tw-flex-col">
                                        <div className="tw-grid sm:tw-grid-cols-2 lg:tw-grid-cols-2 xl:tw-grid-cols-4 tw-gap-4 tw-w-full">
                                            <div className="tw-flex tw-items-start tw-gap-2 tw-border-r">
                                                <p className="tw-font-medium  tw-flex-shrink-0">Status:</p>
                                                {getStatusBadge(data?.form_detail?.status)}
                                            </div>
                                            <div className="tw-flex tw-items-start tw-gap-2">
                                                <p className="tw-font-medium  tw-flex-shrink-0">Version:</p>
                                                {data?.form_detail?.version}
                                            </div>
                                            <div className="tw-col-span-2 tw-hidden xl:tw-block" />
                                            <div className="tw-flex tw-items-start tw-gap-2 tw-col-span-1  tw-border-r">
                                                <p className="tw-font-medium tw-flex-shrink-0">Created By:</p>
                                                {data?.form_detail?.creator?.full_name}
                                            </div>
                                            <div className="tw-flex tw-items-start tw-gap-2 tw-col-span-1 tw-border-r-0 xl:tw-border-r">
                                                <p className="tw-font-medium  tw-flex-shrink-0">Created At:</p>
                                                {formatDateTime(
                                                    data?.form_detail?.created_at,
                                                    FORMAT_DATE.SHOW_DATE_MINUTE
                                                )}
                                            </div>
                                            <div className="tw-flex tw-items-start tw-gap-2 tw-col-span-1 tw-border-r">
                                                <p className="tw-font-medium  tw-flex-shrink-0">Last Updated By:</p>
                                                {data?.form_detail?.updater?.full_name}
                                            </div>
                                            <div className="tw-flex tw-items-start tw-gap-2 tw-col-span-1">
                                                <p className="tw-font-medium  tw-flex-shrink-0">Last Updated At:</p>
                                                <p>
                                                    {data?.form_detail.updated_at
                                                        ? formatDateTime(
                                                              data?.form_detail.updated_at,
                                                              FORMAT_DATE.SHOW_DATE_MINUTE
                                                          )
                                                        : formatDateTime(
                                                              data?.form_detail.created_at,
                                                              FORMAT_DATE.SHOW_DATE_MINUTE
                                                          )}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="tw-flex tw-gap-4 tw-items-center">
                                            <p className="tw-font-medium tw-flex-shrink-0">Used In:</p>
                                            <p
                                                className="role-multiline-ellipsis tw-max-w-full tw-line-clamp-1"
                                                title={usedIn}
                                            >
                                                {usedIn}
                                            </p>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                        <div
                            className={classNames('tw-bg-white tw-flex tw-h-[calc(100vh-376px)] tw-overflow-hidden', {
                                '!tw-h-[calc(100vh-290px)]': isFormAddRoute,
                            })}
                        >
                            <div
                                className={classNames(
                                    'tw-w-full tw-h-[calc(100vh-376px)] tw-overflow-auto tw-py-[40px] tw-px-[60px]',
                                    {
                                        '!tw-h-[calc(100vh-290px)]': isFormAddRoute,
                                    }
                                )}
                            >
                                {!isPreview && (
                                    <FormFields schemaForm={schemaForm} initialData={null} isPreview={isPreview} />
                                )}
                            </div>
                            <FormSettings schemaForm={schemaForm} setSchemaForm={setSchemaForm} />
                        </div>
                        <ModalContent
                            title="Preview Form"
                            show={isPreview}
                            changeShow={onPreviewForm}
                            content={
                                <div className="tw-my-[40px] tw-max-w-[1000px] tw-w-full tw-mx-auto">
                                    <FormFields schemaForm={schemaForm} isPreview={isPreview} />
                                </div>
                            }
                            size="tw-max-w-[1200px]"
                        />
                        <ModalContent
                            title="Edit Form Information"
                            show={isEditNameAndDes}
                            changeShow={onEditFormInformation}
                            content={
                                <form onSubmit={handleSubmit(onSubmit)}>
                                    <div className="tw-w-full tw-mx-auto tw-flex tw-flex-col tw-gap-6">
                                        <div>
                                            <label
                                                className={classNames('form-label tw-text-[14px]')}
                                                htmlFor="formNameInfo"
                                            >
                                                Form Name <span className="tw-text-red-600">*</span>
                                            </label>
                                            <input
                                                id="formNameInfo"
                                                className={classNames('form-control', {
                                                    'is-invalid': Boolean(errors.name?.message),
                                                })}
                                                {...register('name')}
                                            />{' '}
                                            {errors.name && <span className="error">{errors.name?.message}</span>}
                                        </div>
                                        <div>
                                            <label
                                                className={classNames('form-label tw-text-[14px]')}
                                                htmlFor="formDes"
                                            >
                                                Form Description <span className="tw-text-red-600">*</span>
                                            </label>
                                            <textarea
                                                id="formDes"
                                                className={classNames('form-control tw-max-h-[160px]', {
                                                    'is-invalid': Boolean(errors.description?.message),
                                                })}
                                                {...register('description')}
                                            />{' '}
                                            {errors.description && (
                                                <span className="error">{errors.description?.message}</span>
                                            )}
                                        </div>
                                        <div className="tw-gap-4 d-flex justify-content-end">
                                            <button
                                                type="button"
                                                className="btn btn-outline-secondary"
                                                onClick={onEditFormInformation}
                                            >
                                                Close
                                            </button>
                                            <button type="submit" className="btn btn-primary waves-effect waves-light">
                                                Save
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            }
                            size="tw-max-w-[600px]"
                        />
                    </>
                )}
            </div>
        </>
    );
};

export default FormBuilder;
