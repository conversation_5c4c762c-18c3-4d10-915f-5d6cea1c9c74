import { Helmet } from 'react-helmet-async';
import { Fi<PERSON><PERSON><PERSON>, FilterField, TableData } from 'features/dashboard/components/shared';
import { MOCHealthOverdue, MOCHealthBacklog } from 'features/dashboard/components/moc_health';
import { useState, useMemo, useEffect } from 'react';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import { AREAS_LIST } from 'services/AreaService';
import { QUERY_KEY } from 'constants/common';
import { keepPreviousData } from '@tanstack/react-query';
import { AreaQueryRes, AreaType } from 'types/OperationalArea';
import { WorkflowStatus } from '../../../types/Workflow';
import { WORKFLOW_DEFINITION_LIST_ALL } from '../../../services/WorkflowService';
import useQueryParams from '../../../hooks/useQueryParams';
import {
    DashboardMocWorkflowInstancesQuery,
    MocHealthSummaryQuery,
    processFilterConfig,
    SearchProcessDashboardParam,
} from '../../../types/Dashboard';
import omitBy from 'lodash/omitBy';
import { convertDateRangeToQueryParams } from '../../../utils/date';
import isUndefined from 'lodash/isUndefined';
import { generateFilters } from '../../../utils/common';
import { DASHBOARD_MOC_HEALTH_SUMMARY, DASHBOARD_MOC_WORKFLOW_INSTANCE } from '../../../services/DashboardService';
import Spinner from '../../../components/partials/Spinner';

export default function MOCHealth() {
    const [isLoading, setIsLoading] = useState(false);
    const [stepColorMap, setStepColorMap] = useState<Record<number, string>>({});

    // Handle filter changes from FilterBar
    const handleFilterChange = (filters: Record<string, string>) => {
        // TODO: Call MOC Health specific API here with the filter parameters
        console.log('MOCHealth filters:', filters);
    };

    const { data: areaListData } = useGraphQLQuery<AreaQueryRes>(
        [QUERY_KEY.AREAS],
        AREAS_LIST,
        {
            sort: 'type:ASC',
            filters: [`type:=(${AreaType.SUB_AREA})`],
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    const areaOptions = useMemo(() => {
        if (!areaListData?.areas_list) return [];
        return areaListData.areas_list.map((area) => ({
            label: area.name,
            value: area.id,
        }));
    }, [areaListData]);

    const areaList = useMemo(() => areaListData?.areas_list || [], [areaListData]);

    const { data: workflowVersionData } = useGraphQLQuery<{
        workflow_definition_list_all: { id: string; version: string; status: WorkflowStatus }[];
    }>(
        [QUERY_KEY.WORKFLOW_VERSIONS],
        WORKFLOW_DEFINITION_LIST_ALL,
        {
            search: '',
            sort: '',
            filters: [`status:!=(${WorkflowStatus.DRAFT})`],
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );

    const versionActive = useMemo(
        () =>
            workflowVersionData?.workflow_definition_list_all.find(
                (workflow) => workflow.status === WorkflowStatus.ACTIVE
            ),
        [workflowVersionData]
    );

    const workflowVersionOptions = useMemo(() => {
        if (!workflowVersionData?.workflow_definition_list_all) return [];
        return workflowVersionData.workflow_definition_list_all.map((workflow) => ({
            label: `Version ${workflow.version} (${workflow.status})`,
            value: workflow.id,
        }));
    }, [workflowVersionData]);

    // Define filter fields for MOC Process (can be different from Overview)
    const filterFields: FilterField[] = [
        {
            key: 'area_id',
            type: 'select',
            placeholder: 'Area',
            options: areaOptions,
            isMulti: true,
        },
        {
            key: 'workflow_definition_id',
            type: 'select',
            placeholder: 'Workflow Version',
            staticOptions: 'workflow_version',
            options: workflowVersionOptions,
        },
        {
            key: 'current_date',
            type: 'now_static',
        },
        {
            key: 'type',
            type: 'select',
            placeholder: 'Type',
            staticOptions: 'type',
        },
    ];

    const { queryParams, setQueryParams } = useQueryParams<SearchProcessDashboardParam>();

    useEffect(() => {
        if (!queryParams.workflow_definition_id && versionActive) {
            setQueryParams({
                ...queryParams,
                workflow_definition_id: versionActive.id,
            });
        }
    }, [queryParams, setQueryParams, versionActive]);

    const paramConfig: SearchProcessDashboardParam = omitBy(
        {
            created_at_from:
                convertDateRangeToQueryParams(queryParams.created_at__range || '')?.created_at_from ?? undefined,
            created_at_to:
                convertDateRangeToQueryParams(queryParams.created_at__range || '')?.created_at_to ?? undefined,
            area_id: queryParams.area_id,
            type: queryParams.type,
            workflow_definition_id: queryParams.workflow_definition_id ?? versionActive?.id,
            page: queryParams.page ?? '1',
        },
        isUndefined
    );
    const { page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, processFilterConfig);

    const {
        data: mocWorkflowInstanceData,
        isLoading: isLoadingMocWorkflowInstance,
        isFetching: isFetchingMocWorkflowInstance,
    } = useGraphQLQuery<DashboardMocWorkflowInstancesQuery>(
        [QUERY_KEY.DASHBOARD_MOC_INSTANCES, filters, page],
        DASHBOARD_MOC_WORKFLOW_INSTANCE,
        {
            filters: filters.length > 0 ? [...filters, 'is_health:=(true)'] : undefined,
            page: Number(page),
            limit: 10,
            sort: undefined,
            search: undefined,
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );
    const mocWorkflowInstance = useMemo(
        () => mocWorkflowInstanceData?.dashboard_moc_workflow_instances,
        [mocWorkflowInstanceData]
    );

    const handleChangePage = (page: number) => {
        setQueryParams({
            ...paramConfig,
            page: page.toString(),
        });
    };

    const {
        data: mocHealthSummaryData,
        isLoading: isLoadingMocHealthSummary,
        isFetching: isFetchingMocHealthSummary,
    } = useGraphQLQuery<MocHealthSummaryQuery>(
        [QUERY_KEY.DASHBOARD_MOC_HEALTH_SUMMARY, filters],
        DASHBOARD_MOC_HEALTH_SUMMARY,
        {
            filters: filters.length > 0 ? filters : undefined,
            sort: undefined,
            search: undefined,
        },
        '',
        {
            placeholderData: keepPreviousData,
        }
    );
    const mocHealthSummary = useMemo(() => mocHealthSummaryData?.dashboard_moc_health_summary, [mocHealthSummaryData]);

    return (
        <div>
            <Helmet>
                <title>Moc Health</title>
            </Helmet>
            <div className="content-header">
                <div className="row">
                    <div className="col-md-2 col-12 tw-text-3xl tw-flex tw-items-end">MOC Health</div>
                    <div className="col-md-10 col-12">
                        <FilterBar fields={filterFields} isLoading={isLoading} />
                    </div>
                </div>
            </div>

            <div className="content-body mt-3">
                {(isLoadingMocWorkflowInstance ||
                    isLoadingMocHealthSummary ||
                    isFetchingMocWorkflowInstance ||
                    isFetchingMocHealthSummary) && <Spinner />}
                <div className="card">
                    <div className="card-body">
                        {mocHealthSummary && (
                            <>
                                {/* MOC Health Overdue Summary and Chart */}
                                <div className="tw-mb-4">
                                    <MOCHealthOverdue
                                        overdueByStep={mocHealthSummary.overdue_by_step}
                                        overdueByArea={mocHealthSummary.overdue_by_area}
                                        onStepColorMapChange={setStepColorMap}
                                    />
                                </div>

                                {/* MOC Health Backlog Summary and Chart */}
                                <div className="tw-mb-4">
                                    <MOCHealthBacklog backlogByArea={mocHealthSummary.backlog_by_area} />
                                </div>
                            </>
                        )}
                        {mocWorkflowInstance && (
                            <TableData
                                mocWorkflowInstanceData={mocWorkflowInstance}
                                onChangePage={handleChangePage}
                                stepColorMap={stepColorMap}
                            />
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}
