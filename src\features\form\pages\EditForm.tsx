import ContentHeader from 'components/partials/ContentHeader';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import FormBuilder from './FormBuilder';
import { useAuthStore } from '../../../stores/authStore';
import { useMemo } from 'react';
import { AuthGroups } from '../../../types/User';
import { Link, useParams } from 'react-router-dom';

export default function EditForm() {
    const { id } = useParams<{ id: string }>();
    const { t } = useTranslation();
    const currentUser = useAuthStore((state) => state.user);
    const isSuperAdmin = useMemo(() => currentUser?.auth_group === AuthGroups.SUPER_ADMIN, [currentUser?.auth_group]);
    const isAdmin = useMemo(() => currentUser?.auth_group === AuthGroups.ADMIN, [currentUser?.auth_group]);

    return (
        <>
            <Helmet>
                <title>{t('form.edit')}</title>
            </Helmet>

            <ContentHeader title={<Link to="/form">Form</Link>} breadcrumbs={[{ text: 'Edit Form' }]} />
            <FormBuilder isSuperAdmin={isSuperAdmin} isAdmin={isAdmin} id={id} />
        </>
    );
}
